/**
 * UART界面状态管理
 *
 * 定义了UART界面的所有状态数据：
 * 1. 连接状态：
 *    - 设备连接状态
 *    - 连接错误信息
 *    - 重连状态
 *
 * 2. 数据状态：
 *    - 通信记录
 *    - 温湿度数据
 *    - 电池电量
 *
 * 3. 配置状态：
 *    - 当前配置
 *    - 宏命令列表
 *    - 编辑状态
 *
 * 4. 界面状态：
 *    - 显示模式
 *    - 缩放状态
 *    - 错误提示
 */

package no.nordicsemi.android.uart.view

import no.nordicsemi.android.uart.data.UARTConfiguration
import no.nordicsemi.android.uart.data.UARTMacro
import no.nordicsemi.android.uart.data.UARTServiceData

internal data class UARTViewState(
    val editedPosition: Int? = null,
    val selectedConfigurationName: String? = null,
    val isConfigurationEdited: Boolean = false,
    val configurations: List<UARTConfiguration> = emptyList(),
    val uartManagerState: UARTServiceData = UARTServiceData(),
    val isInputVisible: Boolean = true,
    val latestBodyValue: Int? = null,
    val latestAmbientValue: Int? = null,
    val latestEmgValue: Int? = null,
    val latestFilteredEmgValue: Int? = null,
    val isDataProcessing: Boolean = false
) {
    val showEditDialog: Boolean = editedPosition != null

    val selectedConfiguration: UARTConfiguration? = configurations.find { selectedConfigurationName == it.name }

    val selectedMacro: UARTMacro? = selectedConfiguration?.let { configuration ->
        editedPosition?.let {
            configuration.macros.getOrNull(it)
        }
    }
}
