package no.nordicsemi.android.uart.data

import android.util.Log
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.*
import no.nordicsemi.android.kotlin.ble.core.data.BleGattConnectionStatus
import no.nordicsemi.android.kotlin.ble.core.data.GattConnectionStateWithStatus
import kotlin.time.Duration.Companion.milliseconds
import kotlin.math.sqrt
import no.nordicsemi.android.uart.filter.DigitalFilter
import no.nordicsemi.android.uart.filter.DigitalSignalProcessorFactory
import no.nordicsemi.android.uart.filter.FilterConfig
import no.nordicsemi.android.uart.filter.FilterConfigManager
import no.nordicsemi.android.uart.filter.FilterPresets



private class CircularBuffer<T>(private val capacity: Int) {
    private val buffer = Array<Any?>(capacity) { null }
    private var writeIndex = 0
    private var size = 0
    private var lastReadIndex = 0

    fun add(element: T) {
        buffer[writeIndex] = element
        writeIndex = (writeIndex + 1) % capacity
        size = minOf(size + 1, capacity)
    }

    fun addAll(elements: Collection<T>) {
        elements.forEach { add(it) }
    }

    fun getAll(): List<T> = getLatest(size)

    fun getNewData(): List<T> {
        if (size == 0) return emptyList()

        val result = mutableListOf<T>()
        var currentIndex = lastReadIndex

        while (currentIndex != writeIndex) {
            buffer[currentIndex]?.let { result.add(it as T) }
            currentIndex = (currentIndex + 1) % capacity
        }

        lastReadIndex = writeIndex

        return result
    }

    fun getLatest(count: Int): List<T> {
        if (size == 0) return emptyList()

        val result = mutableListOf<T>()
        var readIndex = if (writeIndex == 0) capacity - 1 else writeIndex - 1

        repeat(minOf(count, size)) {
            @Suppress("UNCHECKED_CAST")
            buffer[readIndex]?.let { result.add(it as T) }
            readIndex = if (readIndex == 0) capacity - 1 else readIndex - 1
        }
        return result.reversed()
    }

    fun clear() {
        buffer.fill(null)
        writeIndex = 0
        size = 0
        lastReadIndex = 0
    }

    fun size() = size

    fun isEmpty() = size == 0
}

private object UARTBufferManager {
    private const val RAW_BUFFER_SIZE = 2000

    // 原始数据缓冲区
    private val rawChannel1Buffer = CircularBuffer<Int>(RAW_BUFFER_SIZE)
    private val rawChannel2Buffer = CircularBuffer<Int>(RAW_BUFFER_SIZE)
    private val rawChannel3Buffer = CircularBuffer<Int>(RAW_BUFFER_SIZE)

    // 滤波后数据缓冲区
    private val filteredChannel3Buffer = CircularBuffer<Int>(RAW_BUFFER_SIZE)

    // 滤波器配置管理器
    private val filterConfigManager = FilterConfigManager()

    // EMG滤波器实例
    private var emgFilter: DigitalFilter? = null

    init {
        // 初始化滤波器配置监听器
        filterConfigManager.addConfigChangeListener { config ->
            updateEMGFilter(config)
        }

        // 设置默认滤波器配置
        filterConfigManager.setConfig(FilterPresets.NO_FILTER)
    }

    /**
     * 更新EMG滤波器
     */
    private fun updateEMGFilter(config: FilterConfig) {
        emgFilter = if (config.enabled && config.type != no.nordicsemi.android.uart.filter.FilterType.NONE) {
            when (config.type) {
                no.nordicsemi.android.uart.filter.FilterType.LOW_PASS ->
                    DigitalSignalProcessorFactory.createEMGLowPassFilter(
                        config.getValidatedCutoffFrequency(),
                        config.sampleRate,
                        config.order
                    )
                no.nordicsemi.android.uart.filter.FilterType.HIGH_PASS ->
                    DigitalSignalProcessorFactory.createEMGHighPassFilter(
                        config.getValidatedCutoffFrequency(),
                        config.sampleRate,
                        config.order
                    )
                no.nordicsemi.android.uart.filter.FilterType.BAND_PASS -> {
                    val (lowCutoff, highCutoff) = config.getValidatedBandPassRange()
                    DigitalSignalProcessorFactory.createEMGBandPassFilter(
                        lowCutoff,
                        highCutoff,
                        config.sampleRate,
                        config.order
                    )
                }
                else -> null
            }
        } else {
            null
        }

        // 重置滤波器状态
        emgFilter?.reset()
    }

    fun addBodyData(data: Int) = rawChannel1Buffer.add(data)
    fun addAmbientData(data: Int) = rawChannel2Buffer.add(data)

    fun addEmgData(data: Int) {
        // 添加原始数据
        rawChannel3Buffer.add(data)

        // 如果启用滤波，则进行滤波处理
        val filteredValue = emgFilter?.let { filter ->
            filter.filter(data.toFloat()).toInt()
        } ?: data

        // 添加滤波后的数据
        filteredChannel3Buffer.add(filteredValue)
    }

    fun getNewBodyData(): List<Int> = rawChannel1Buffer.getNewData()
    fun getNewAmbientData(): List<Int> = rawChannel2Buffer.getNewData()
    fun getNewEmgData(): List<Int> = rawChannel3Buffer.getNewData()
    fun getNewFilteredEmgData(): List<Int> = filteredChannel3Buffer.getNewData()

    fun getAllBodyData(): List<Int> = rawChannel1Buffer.getAll()
    fun getAllAmbientData(): List<Int> = rawChannel2Buffer.getAll()
    fun getAllEmgData(): List<Int> = rawChannel3Buffer.getAll()
    fun getAllFilteredEmgData(): List<Int> = filteredChannel3Buffer.getAll()

    fun clearAll() {
        rawChannel1Buffer.clear()
        rawChannel2Buffer.clear()
        rawChannel3Buffer.clear()
        filteredChannel3Buffer.clear()
        emgFilter?.reset()
    }

    /**
     * 获取滤波器配置管理器
     */
    fun getFilterConfigManager(): FilterConfigManager = filterConfigManager

    /**
     * 检查滤波器是否启用
     */
    fun isFilterEnabled(): Boolean = filterConfigManager.isFilterEnabled()
}

internal data class UARTServiceData(
    val messages: List<UARTRecord> = emptyList(),
    val connectionState: GattConnectionStateWithStatus? = null,
    val batteryLevel: Int? = null,
    val deviceName: String? = null,
    val missingServices: Boolean = false,
    val bodySensorLocation: Int? = null,
    val zoomIn: Boolean = false,
) {
    private val TAG = "UARTServiceData"

    val disconnectStatus = if (missingServices) {
        BleGattConnectionStatus.NOT_SUPPORTED
    } else {
        connectionState?.status ?: BleGattConnectionStatus.UNKNOWN
    }

    companion object {
        private const val MIN_UPDATE_INTERVAL = 100L
    }

    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.Default)
    private val messagesFlow = MutableStateFlow(messages)

    //数据接收处理
    suspend fun receiveData(data: String) {
        withContext(Dispatchers.Default) {
            try {
                val bytes = data.toByteArray(Charsets.ISO_8859_1)

                // 检查数据包长度是否为184字节（嵌入式设备的完整数据包）
                if (bytes.size == 184) {
                    // 解析完整的184字节数据包
                    val parsedPacket = parseFullPacket(bytes)
                    withContext(Dispatchers.IO) {
                        processFullPacket(parsedPacket)
                    }
                } else {
                    // 兼容旧的解析方式（每4字节一组双通道数据）
                    val parsedValues = parseRawData(data)
                    withContext(Dispatchers.IO) {
                        processValues(parsedValues)
                    }
                }
            } catch (e: Exception) {
//                Log.e("UART_DEBUG", "数据处理错误: ${e.message}")
            }
        }
    }

    /**
     * 将两个字节转换为一个整数
     *
     * @param highByte 高字节
     * @param lowByte 低字节
     * @return 转换后的整数值
     */
    private fun bytesToInt(highByte: Byte, lowByte: Byte): Int {
        return (highByte.toInt() and 0xFF) * 256 + (lowByte.toInt() and 0xFF)
    }

    /**
     * 格式化数字为5位字符串
     *
     * 将数字格式化为固定5位的字符串,不足位数在前面补0
     *
     * @param number 要格式化的数字
     * @return 格式化后的5位字符串
     */
    private fun formatNumber(number: Int): String {
        return when {
            number < 10 -> "0000$number"
            number < 100 -> "000$number"
            number < 1000 -> "00$number"
            number < 10000 -> "0$number"
            else -> number.toString()
        }
    }

    /**
     * 解析后的数据包类
     *
     * @property timestamp 时间戳
     * @property soundData1 声音数据1列表 (30个点)
     * @property soundData2 声音数据2列表 (30个点)
     * @property ecgData ECG数据列表 (30个点)
     */
    private data class ParsedPacket(
        val timestamp: Long,
        val soundData1: List<Int>,
        val soundData2: List<Int>,
        val ecgData: List<Int>
    )

    /**
     * 解析后的双通道数据值类（保持向后兼容）
     *
     * @property channel1Value 通道1的数据值
     * @property channel2Value 通道2的数据值
     */
    private data class ParsedValues(
        val channel1Value: Int,
        val channel2Value: Int
    )

    /**
     * 解析完整的184字节数据包
     *
     * 数据包结构：
     * - 字节 0-3: 时间戳 (4字节)
     * - 字节 4-63: 声音数据1 (30个点 × 2字节 = 60字节)
     * - 字节 64-123: 声音数据2 (30个点 × 2字节 = 60字节)
     * - 字节 124-183: ECG数据 (30个点 × 2字节 = 60字节)
     *
     * @param bytes 184字节的原始数据
     * @return 解析后的数据包
     */
    private fun parseFullPacket(bytes: ByteArray): ParsedPacket {
        // 解析时间戳（前4字节，大端序）
        val timestamp = ((bytes[0].toInt() and 0xFF) shl 24) or
                ((bytes[1].toInt() and 0xFF) shl 16) or
                ((bytes[2].toInt() and 0xFF) shl 8) or
                (bytes[3].toInt() and 0xFF)

        // 解析声音数据1（字节4-63，30个点）
        val soundData1 = mutableListOf<Int>()
        for (i in 0 until 30) {
            val index = 4 + i * 2
            val value = bytesToInt(bytes[index], bytes[index + 1])
            soundData1.add(value)
        }

        // 解析声音数据2（字节64-123，30个点）
        val soundData2 = mutableListOf<Int>()
        for (i in 0 until 30) {
            val index = 64 + i * 2
            val value = bytesToInt(bytes[index], bytes[index + 1])
            soundData2.add(value)
        }

        // 解析ECG数据（字节124-183，30个点）
        val ecgData = mutableListOf<Int>()
        for (i in 0 until 30) {
            val index = 124 + i * 2
            val value = bytesToInt(bytes[index], bytes[index + 1])
            ecgData.add(value)
        }

        return ParsedPacket(
            timestamp = timestamp.toLong(),
            soundData1 = soundData1,
            soundData2 = soundData2,
            ecgData = ecgData
        )
    }

    /**
     * 解析原始数据字符串（兼容旧格式）
     *
     * 该函数将原始字符串数据解析为双通道数值列表:
     * 1. 将字符串转换为字节数组
     * 2. 每4个字节解析为一组双通道数据
     * 3. 返回解析后的数据列表
     *
     * @param data 原始数据字符串
     * @return 解析后的双通道数值列表
     */
    private fun parseRawData(data: String): List<ParsedValues> {
        val result = mutableListOf<ParsedValues>()
        try {
            val bytes = data.toByteArray(Charsets.ISO_8859_1)
            var i = 0
            while (i < bytes.size - 3) {
                val value1 = bytesToInt(bytes[i], bytes[i + 1])
                val value2 = bytesToInt(bytes[i + 2], bytes[i + 3])
                result.add(ParsedValues(value1, value2))
                i += 4
            }
        } catch (e: Exception) {
        }
        return result
    }
    /**
     * 处理完整数据包
     *
     * 该函数将解析后的完整数据包添加到对应的缓冲区:
     * 1. soundData1添加到body数据缓冲区
     * 2. soundData2添加到ambient数据缓冲区
     * 3. ecgData添加到EMG数据缓冲区
     *
     * @param packet 解析后的完整数据包
     */
    private fun processFullPacket(packet: ParsedPacket) {
        // 添加声音数据1到body缓冲区
        packet.soundData1.forEach { value ->
            UARTBufferManager.addBodyData(value)
        }

        // 添加声音数据2到ambient缓冲区
        packet.soundData2.forEach { value ->
            UARTBufferManager.addAmbientData(value)
        }

        // 添加ECG数据到EMG缓冲区
        packet.ecgData.forEach { value ->
            UARTBufferManager.addEmgData(value)
        }
    }

    /**
     * 处理解析后的双通道数据值（兼容旧格式）
     *
     * 该函数将解析后的数据添加到缓冲区:
     * 1. channel1Value添加到body数据缓冲区
     * 2. channel2Value添加到ambient数据缓冲区
     *
     * @param values 解析后的双通道数据值列表
     */
    private fun processValues(values: List<ParsedValues>) {
        values.forEachIndexed { index, parsedValue ->
            UARTBufferManager.addBodyData(parsedValue.channel1Value)
            UARTBufferManager.addAmbientData(parsedValue.channel2Value)
        }
    }

    /**
     * 将UART记录转换为显示消息（数据摘要模式）
     */
    val displayMessages = messages.map { record ->
        if (record.type == UARTRecordType.INPUT) {
            record
        } else {
            // 显示数据包的基本信息而不是全部内容
            val bytes = record.text.toByteArray(Charsets.ISO_8859_1)
            val dataSize = bytes.size
            
            val summaryText = when {
                dataSize == 184 -> {
                    // 解析时间戳（前4字节）
                    val timestamp = if (bytes.size >= 4) {
                        ((bytes[0].toInt() and 0xFF) shl 24) or
                        ((bytes[1].toInt() and 0xFF) shl 16) or
                        ((bytes[2].toInt() and 0xFF) shl 8) or
                        (bytes[3].toInt() and 0xFF)
                    } else 0
                    "📦 Full Packet (184 bytes) - Timestamp: $timestamp"
                }
                dataSize >= 4 && dataSize % 4 == 0 -> {
                    val pairCount = dataSize / 4
                    "📊 Data Packet ($dataSize bytes, $pairCount pairs)"
                }
                dataSize > 0 -> {
                    "📄 Raw Data ($dataSize bytes)"
                }
                else -> {
                    "⚠️ Empty Data"
                }
            }
            
            record.copy(text = summaryText)
        }
    }

    val bodyMessages: List<Int>
        get() = UARTBufferManager.getAllBodyData()

    val ambientMessages: List<Int>
        get() = UARTBufferManager.getAllAmbientData()

    val emgMessages: List<Int>
        get() = UARTBufferManager.getAllEmgData()

    val filteredEmgMessages: List<Int>
        get() = UARTBufferManager.getAllFilteredEmgData()

    val recordBodyMessages: List<Int>
        get() = UARTBufferManager.getNewBodyData()

    val recordAmbientMessages: List<Int>
        get() = UARTBufferManager.getNewAmbientData()

    val recordEmgMessages: List<Int>
        get() = UARTBufferManager.getNewEmgData()

    val recordFilteredEmgMessages: List<Int>
        get() = UARTBufferManager.getNewFilteredEmgData()

    /**
     * 获取滤波器配置管理器
     */
    fun getFilterConfigManager(): FilterConfigManager = UARTBufferManager.getFilterConfigManager()

    /**
     * 检查EMG滤波器是否启用
     */
    fun isEmgFilterEnabled(): Boolean = UARTBufferManager.isFilterEnabled()

    fun cleanup() {
        scope.cancel()
        UARTBufferManager.clearAll()
    }
}

internal data class UARTRecord(
    val text: String,
    val type: UARTRecordType,
    val timestamp: Long = System.currentTimeMillis()
)

enum class UARTRecordType {
    INPUT, OUTPUT
}