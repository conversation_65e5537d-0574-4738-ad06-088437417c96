/**
 * 数字信号处理器
 *
 * 提供实时数字滤波功能，支持多种滤波器类型：
 * 1. 低通滤波器 - 去除高频噪声
 * 2. 高通滤波器 - 去除低频漂移
 * 3. 带通滤波器 - 保留特定频段
 *
 * 特性：
 * - 实时流式处理
 * - 低延迟IIR滤波器
 * - 可配置参数
 * - 状态管理和重置
 */

package no.nordicsemi.android.uart.filter

import kotlin.math.*

/**
 * 数字滤波器接口
 */
interface DigitalFilter {
    /**
     * 滤波处理单个数据点
     * @param input 输入数据
     * @return 滤波后的数据
     */
    fun filter(input: Float): Float
    
    /**
     * 重置滤波器状态
     */
    fun reset()
    
    /**
     * 获取滤波器延迟（样本数）
     */
    fun getDelay(): Int
}

/**
 * 低通滤波器实现
 * 使用二阶IIR巴特沃斯滤波器
 */
class LowPassFilter(
    private val cutoffFrequency: Float,
    private val sampleRate: Float,
    private val order: Int = 2
) : DigitalFilter {
    
    private val sections: Array<BiquadSection>
    private val numSections: Int
    
    init {
        require(cutoffFrequency > 0) { "截止频率必须大于0" }
        require(sampleRate > 0) { "采样率必须大于0" }
        require(cutoffFrequency < sampleRate / 2) { "截止频率必须小于奈奎斯特频率" }
        require(order in 1..8) { "滤波器阶数必须在1-8之间" }
        
        numSections = (order + 1) / 2
        sections = Array(numSections) { BiquadSection() }
        
        calculateCoefficients()
    }
    
    /**
     * 计算巴特沃斯低通滤波器系数
     */
    private fun calculateCoefficients() {
        val wc = 2.0 * PI * cutoffFrequency / sampleRate
        val k = tan(wc / 2.0)
        
        for (i in 0 until numSections) {
            val pole = if (order % 2 == 1 && i == 0) {
                // 奇数阶的第一个极点是实数
                -1.0
            } else {
                // 复数极点对
                val n = if (order % 2 == 1) i.toDouble() else i.toDouble() + 0.5
                val angle = PI * (2.0 * n + order.toDouble() + 1.0) / (2.0 * order.toDouble())
                cos(angle)
            }
            
            if (order % 2 == 1 && i == 0) {
                // 一阶段
                val a1 = (k - 1) / (k + 1)
                val b0 = k / (k + 1)
                val b1 = k / (k + 1)
                
                sections[i].setCoefficients(b0, b1, 0.0, a1, 0.0)
            } else {
                // 二阶段
                val q = -0.5 / pole
                val norm = 1 + k / q + k * k
                val b0 = k * k / norm
                val b1 = 2 * b0
                val b2 = b0
                val a1 = (2 * (k * k - 1)) / norm
                val a2 = (1 - k / q + k * k) / norm
                
                sections[i].setCoefficients(b0, b1, b2, a1, a2)
            }
        }
    }
    
    override fun filter(input: Float): Float {
        var output = input.toDouble()
        for (section in sections) {
            output = section.process(output)
        }
        return output.toFloat()
    }
    
    override fun reset() {
        sections.forEach { it.reset() }
    }
    
    override fun getDelay(): Int {
        return order
    }
}

/**
 * 高通滤波器实现
 */
class HighPassFilter(
    private val cutoffFrequency: Float,
    private val sampleRate: Float,
    private val order: Int = 2
) : DigitalFilter {
    
    private val sections: Array<BiquadSection>
    private val numSections: Int
    
    init {
        require(cutoffFrequency > 0) { "截止频率必须大于0" }
        require(sampleRate > 0) { "采样率必须大于0" }
        require(cutoffFrequency < sampleRate / 2) { "截止频率必须小于奈奎斯特频率" }
        require(order in 1..8) { "滤波器阶数必须在1-8之间" }
        
        numSections = (order + 1) / 2
        sections = Array(numSections) { BiquadSection() }
        
        calculateCoefficients()
    }
    
    private fun calculateCoefficients() {
        val wc = 2.0 * PI * cutoffFrequency / sampleRate
        val k = tan(wc / 2.0)
        
        for (i in 0 until numSections) {
            val pole = if (order % 2 == 1 && i == 0) {
                -1.0
            } else {
                val n = if (order % 2 == 1) i.toDouble() else i.toDouble() + 0.5
                val angle = PI * (2.0 * n + order.toDouble() + 1.0) / (2.0 * order.toDouble())
                cos(angle)
            }
            
            if (order % 2 == 1 && i == 0) {
                // 一阶高通段
                val a1 = (k - 1) / (k + 1)
                val b0 = 1 / (k + 1)
                val b1 = -1 / (k + 1)
                
                sections[i].setCoefficients(b0, b1, 0.0, a1, 0.0)
            } else {
                // 二阶高通段
                val q = -0.5 / pole
                val norm = 1 + k / q + k * k
                val b0 = 1 / norm
                val b1 = -2 * b0
                val b2 = b0
                val a1 = (2 * (k * k - 1)) / norm
                val a2 = (1 - k / q + k * k) / norm
                
                sections[i].setCoefficients(b0, b1, b2, a1, a2)
            }
        }
    }
    
    override fun filter(input: Float): Float {
        var output = input.toDouble()
        for (section in sections) {
            output = section.process(output)
        }
        return output.toFloat()
    }
    
    override fun reset() {
        sections.forEach { it.reset() }
    }
    
    override fun getDelay(): Int {
        return order
    }
}

/**
 * 带通滤波器实现
 */
class BandPassFilter(
    private val lowCutoff: Float,
    private val highCutoff: Float,
    private val sampleRate: Float,
    private val order: Int = 2
) : DigitalFilter {
    
    private val highPassFilter: HighPassFilter
    private val lowPassFilter: LowPassFilter
    
    init {
        require(lowCutoff > 0) { "低截止频率必须大于0" }
        require(highCutoff > lowCutoff) { "高截止频率必须大于低截止频率" }
        require(highCutoff < sampleRate / 2) { "高截止频率必须小于奈奎斯特频率" }
        
        highPassFilter = HighPassFilter(lowCutoff, sampleRate, order)
        lowPassFilter = LowPassFilter(highCutoff, sampleRate, order)
    }
    
    override fun filter(input: Float): Float {
        val highPassed = highPassFilter.filter(input)
        return lowPassFilter.filter(highPassed)
    }
    
    override fun reset() {
        highPassFilter.reset()
        lowPassFilter.reset()
    }
    
    override fun getDelay(): Int {
        return highPassFilter.getDelay() + lowPassFilter.getDelay()
    }
}

/**
 * 二阶段滤波器节
 * 实现标准的二阶IIR滤波器结构
 */
private class BiquadSection {
    // 系数
    private var b0 = 0.0
    private var b1 = 0.0
    private var b2 = 0.0
    private var a1 = 0.0
    private var a2 = 0.0
    
    // 延迟线
    private var x1 = 0.0
    private var x2 = 0.0
    private var y1 = 0.0
    private var y2 = 0.0
    
    fun setCoefficients(b0: Double, b1: Double, b2: Double, a1: Double, a2: Double) {
        this.b0 = b0
        this.b1 = b1
        this.b2 = b2
        this.a1 = a1
        this.a2 = a2
    }
    
    fun process(input: Double): Double {
        val output = b0 * input + b1 * x1 + b2 * x2 - a1 * y1 - a2 * y2
        
        // 更新延迟线
        x2 = x1
        x1 = input
        y2 = y1
        y1 = output
        
        return output
    }
    
    fun reset() {
        x1 = 0.0
        x2 = 0.0
        y1 = 0.0
        y2 = 0.0
    }
}

/**
 * 数字信号处理器工厂
 */
object DigitalSignalProcessorFactory {

    /**
     * 创建EMG信号专用低通滤波器
     */
    fun createEMGLowPassFilter(
        cutoffFrequency: Float = 100f,
        sampleRate: Float = 2000f,
        order: Int = 2
    ): DigitalFilter {
        return LowPassFilter(cutoffFrequency, sampleRate, order)
    }

    /**
     * 创建EMG信号专用高通滤波器（去除基线漂移）
     */
    fun createEMGHighPassFilter(
        cutoffFrequency: Float = 10f,
        sampleRate: Float = 2000f,
        order: Int = 2
    ): DigitalFilter {
        return HighPassFilter(cutoffFrequency, sampleRate, order)
    }

    /**
     * 创建EMG信号专用带通滤波器
     */
    fun createEMGBandPassFilter(
        lowCutoff: Float = 10f,
        highCutoff: Float = 100f,
        sampleRate: Float = 2000f,
        order: Int = 2
    ): DigitalFilter {
        return BandPassFilter(lowCutoff, highCutoff, sampleRate, order)
    }
}
