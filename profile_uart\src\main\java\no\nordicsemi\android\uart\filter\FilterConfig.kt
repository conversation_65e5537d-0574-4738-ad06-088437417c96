/**
 * 滤波器配置管理
 *
 * 提供滤波器参数配置和管理功能：
 * 1. 滤波器类型定义
 * 2. 预设配置选项
 * 3. 自定义参数验证
 * 4. 配置序列化和反序列化
 *
 * 特性：
 * - 类型安全的配置管理
 * - 预设的EMG信号滤波配置
 * - 参数验证和边界检查
 * - 配置持久化支持
 */

package no.nordicsemi.android.uart.filter

/**
 * 滤波器类型枚举
 */
enum class FilterType(val displayName: String, val description: String) {
    LOW_PASS("低通滤波", "去除高频噪声，保留低频信号"),
    HIGH_PASS("高通滤波", "去除低频漂移，保留高频信号"),
    BAND_PASS("带通滤波", "保留特定频段，去除其他频率"),
    NONE("无滤波", "不进行任何滤波处理")
}

/**
 * 滤波器配置数据类
 */
data class FilterConfig(
    val type: FilterType = FilterType.LOW_PASS,
    val cutoffFrequency: Float = 100f,
    val lowCutoffFrequency: Float = 10f,  // 带通滤波器用
    val highCutoffFrequency: Float = 100f, // 带通滤波器用
    val order: Int = 2,
    val sampleRate: Float = 2000f,
    val enabled: Boolean = false
) {
    
    /**
     * 验证配置参数的有效性
     */
    fun validate(): ValidationResult {
        val errors = mutableListOf<String>()
        
        // 验证采样率
        if (sampleRate <= 0) {
            errors.add("采样率必须大于0")
        }
        
        // 验证滤波器阶数
        if (order !in 1..8) {
            errors.add("滤波器阶数必须在1-8之间")
        }
        
        val nyquistFreq = sampleRate / 2
        
        when (type) {
            FilterType.LOW_PASS, FilterType.HIGH_PASS -> {
                if (cutoffFrequency <= 0) {
                    errors.add("截止频率必须大于0")
                }
                if (cutoffFrequency >= nyquistFreq) {
                    errors.add("截止频率必须小于奈奎斯特频率 (${nyquistFreq}Hz)")
                }
            }
            FilterType.BAND_PASS -> {
                if (lowCutoffFrequency <= 0) {
                    errors.add("低截止频率必须大于0")
                }
                if (highCutoffFrequency <= lowCutoffFrequency) {
                    errors.add("高截止频率必须大于低截止频率")
                }
                if (highCutoffFrequency >= nyquistFreq) {
                    errors.add("高截止频率必须小于奈奎斯特频率 (${nyquistFreq}Hz)")
                }
            }
            FilterType.NONE -> {
                // 无需验证
            }
        }
        
        return if (errors.isEmpty()) {
            ValidationResult.Success
        } else {
            ValidationResult.Error(errors)
        }
    }
    
    /**
     * 获取有效的截止频率（确保在合理范围内）
     */
    fun getValidatedCutoffFrequency(): Float {
        val nyquistFreq = sampleRate / 2
        return cutoffFrequency.coerceIn(1f, nyquistFreq - 1f)
    }
    
    /**
     * 获取有效的带通滤波器频率范围
     */
    fun getValidatedBandPassRange(): Pair<Float, Float> {
        val nyquistFreq = sampleRate / 2
        val validLow = lowCutoffFrequency.coerceIn(1f, nyquistFreq - 2f)
        val validHigh = highCutoffFrequency.coerceIn(validLow + 1f, nyquistFreq - 1f)
        return Pair(validLow, validHigh)
    }
}

/**
 * 配置验证结果
 */
sealed class ValidationResult {
    object Success : ValidationResult()
    data class Error(val errors: List<String>) : ValidationResult()
}

/**
 * 预设滤波器配置
 */
object FilterPresets {
    
    /**
     * EMG信号标准低通滤波配置
     */
    val EMG_STANDARD_LOW_PASS = FilterConfig(
        type = FilterType.LOW_PASS,
        cutoffFrequency = 100f,
        order = 2,
        sampleRate = 2000f,
        enabled = true
    )
    
    /**
     * EMG信号强力去噪配置
     */
    val EMG_STRONG_NOISE_REDUCTION = FilterConfig(
        type = FilterType.LOW_PASS,
        cutoffFrequency = 50f,
        order = 4,
        sampleRate = 2000f,
        enabled = true
    )
    
    /**
     * EMG信号轻度滤波配置
     */
    val EMG_LIGHT_FILTERING = FilterConfig(
        type = FilterType.LOW_PASS,
        cutoffFrequency = 200f,
        order = 1,
        sampleRate = 2000f,
        enabled = true
    )
    
    /**
     * EMG信号带通滤波配置（去除基线漂移和高频噪声）
     */
    val EMG_BAND_PASS = FilterConfig(
        type = FilterType.BAND_PASS,
        lowCutoffFrequency = 10f,
        highCutoffFrequency = 100f,
        order = 2,
        sampleRate = 2000f,
        enabled = true
    )

    /**
     * 心电信号带通滤波配置（5Hz~40Hz）
     */
    val ECG_BAND_PASS = FilterConfig(
        type = FilterType.BAND_PASS,
        lowCutoffFrequency = 5f,
        highCutoffFrequency = 40f,
        order = 2,
        sampleRate = 2000f,
        enabled = true
    )
    
    /**
     * EMG信号高通滤波配置（仅去除基线漂移）
     */
    val EMG_HIGH_PASS = FilterConfig(
        type = FilterType.HIGH_PASS,
        cutoffFrequency = 10f,
        order = 2,
        sampleRate = 2000f,
        enabled = true
    )
    
    /**
     * 无滤波配置
     */
    val NO_FILTER = FilterConfig(
        type = FilterType.NONE,
        enabled = false
    )
    
    /**
     * 获取所有预设配置
     */
    fun getAllPresets(): List<Pair<String, FilterConfig>> {
        return listOf(
            "标准低通滤波" to EMG_STANDARD_LOW_PASS,
            "强力去噪" to EMG_STRONG_NOISE_REDUCTION,
            "轻度滤波" to EMG_LIGHT_FILTERING,
            "EMG带通滤波" to EMG_BAND_PASS,
            "心电带通滤波" to ECG_BAND_PASS,
            "高通滤波" to EMG_HIGH_PASS,
            "无滤波" to NO_FILTER
        )
    }
}

/**
 * 滤波器配置管理器
 */
class FilterConfigManager {
    
    private var currentConfig = FilterPresets.NO_FILTER
    private val configChangeListeners = mutableListOf<(FilterConfig) -> Unit>()
    
    /**
     * 获取当前配置
     */
    fun getCurrentConfig(): FilterConfig = currentConfig
    
    /**
     * 设置新的配置
     */
    fun setConfig(config: FilterConfig): Boolean {
        val validationResult = config.validate()
        return when (validationResult) {
            is ValidationResult.Success -> {
                currentConfig = config
                notifyConfigChanged()
                true
            }
            is ValidationResult.Error -> {
                // 可以在这里记录错误日志
                false
            }
        }
    }
    
    /**
     * 应用预设配置
     */
    fun applyPreset(preset: FilterConfig): Boolean {
        return setConfig(preset)
    }
    
    /**
     * 添加配置变化监听器
     */
    fun addConfigChangeListener(listener: (FilterConfig) -> Unit) {
        configChangeListeners.add(listener)
    }
    
    /**
     * 移除配置变化监听器
     */
    fun removeConfigChangeListener(listener: (FilterConfig) -> Unit) {
        configChangeListeners.remove(listener)
    }
    
    /**
     * 通知配置变化
     */
    private fun notifyConfigChanged() {
        configChangeListeners.forEach { it(currentConfig) }
    }
    
    /**
     * 重置为默认配置
     */
    fun resetToDefault() {
        setConfig(FilterPresets.NO_FILTER)
    }
    
    /**
     * 检查当前配置是否启用滤波
     */
    fun isFilterEnabled(): Boolean = currentConfig.enabled && currentConfig.type != FilterType.NONE
    
    /**
     * 快速切换滤波开关
     */
    fun toggleFilter(): Boolean {
        val newConfig = currentConfig.copy(enabled = !currentConfig.enabled)
        return setConfig(newConfig)
    }
}

/**
 * 滤波器配置工具类
 */
object FilterConfigUtils {
    
    /**
     * 根据信号特征推荐滤波器配置
     */
    fun recommendConfigForEMG(
        signalNoiseLevel: NoiseLevel = NoiseLevel.MEDIUM,
        preserveHighFreq: Boolean = false
    ): FilterConfig {
        return when (signalNoiseLevel) {
            NoiseLevel.LOW -> if (preserveHighFreq) {
                FilterPresets.EMG_LIGHT_FILTERING
            } else {
                FilterPresets.EMG_STANDARD_LOW_PASS
            }
            NoiseLevel.MEDIUM -> FilterPresets.EMG_STANDARD_LOW_PASS
            NoiseLevel.HIGH -> FilterPresets.EMG_STRONG_NOISE_REDUCTION
        }
    }
    
    /**
     * 计算滤波器的理论延迟
     */
    fun calculateFilterDelay(config: FilterConfig): Int {
        return when (config.type) {
            FilterType.LOW_PASS, FilterType.HIGH_PASS -> config.order
            FilterType.BAND_PASS -> config.order * 2
            FilterType.NONE -> 0
        }
    }
    
    /**
     * 估算滤波器的计算复杂度
     */
    fun estimateComputationalCost(config: FilterConfig): ComputationalCost {
        return when (config.type) {
            FilterType.NONE -> ComputationalCost.NONE
            FilterType.LOW_PASS, FilterType.HIGH_PASS -> when (config.order) {
                1 -> ComputationalCost.LOW
                2 -> ComputationalCost.MEDIUM
                in 3..4 -> ComputationalCost.HIGH
                else -> ComputationalCost.VERY_HIGH
            }
            FilterType.BAND_PASS -> when (config.order) {
                1 -> ComputationalCost.MEDIUM
                2 -> ComputationalCost.HIGH
                else -> ComputationalCost.VERY_HIGH
            }
        }
    }
}

/**
 * 噪声水平枚举
 */
enum class NoiseLevel {
    LOW, MEDIUM, HIGH
}

/**
 * 计算复杂度枚举
 */
enum class ComputationalCost {
    NONE, LOW, MEDIUM, HIGH, VERY_HIGH
}
